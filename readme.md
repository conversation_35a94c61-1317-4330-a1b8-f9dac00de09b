# Data for the VLSP 2025 DRiLL: The challenge of Deep Retrieval in the expansive Legal Landscape
- The contents of the legal passages whose `id` is mentioned in the training are provided in `legal_corpus.json`.
- The data is provided in JSON format and UTF-8 encoding.
- Please refer to https://vlsp.org.vn/vlsp2025/eval/drill for more information. 

## File Overview and Structure

### 1. `train.json`
- **Decription**: Annotated training dataset.  
- **Schema**:
  ```json
  {
    "qid": <integer>,             // Unique question identifier
    "question": "<string>",       // Text of the question
    "relevant_laws": [<integer>]  // List of `aid` values (article IDs) from `legal_corpus.json`
  }

### 2. `legal_corpus.json`
- **Purpose**: Legal articlecorpus.
- **Structure**:
  ```json
  {
    "id": <integer>,               // Unique document identifier
    "law_id": <integer>,           // Official law number
    "content": [
      {
        "aid": <integer>,          // Article identifier within this document
        "content_Article": "<string>" // Full text of the article
      }
      // ... additional articles
    ]
  }


Task Description
With the rapid advancement of artificial intelligence, particularly generative models in natural language processing (NLP) such as ChatGPT, DeepSeek, and Qwen, the demand for intelligent tools to process legal texts is growing significantly. While Legal NLP research has seen substantial progress in languages like English, Japanese, and Chinese, foundational research for Vietnamese legal text processing remains relatively underdeveloped. In this shared task, we introduce one of the first initiatives aimed at advancing Vietnamese Legal NLP. 

Information Retrieval (IR) is a core task in NLP, concerned with identifying which pieces of information are most relevant to a given query. In the legal domain, the Legal Document Retrieval task focuses on determining which legal articles are relevant to a specific legal question. The task can be formalized as follows: Given a set of questions Q = {q1, q2, ..., qn} and a corpus of articles A = {a1, a2, ..., an) the task is required to identify a a subset A′ ⊂ A where each article ai ∈ A' is considered “relevant” to the corresponding question q.

We call an article “Relevant” to a query if the query sentence can be answered Yes/No, entailed from the meaning of the article. 

LLMs Usages
You can use LLMs whose training data and/or model are publicly available (e.g. Huggingface or similar sites), but you cannot use LLMs whose models are closed (e.g. GPT-4o, Gemini, ...). For reproducibility purposes, please include information on how to obtain the model in the paper. 

Evaluation metrics 
Automatic Evaluation: Recall, Precision, Macro-F2
Human Evaluation 